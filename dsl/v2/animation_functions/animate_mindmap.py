import json
import os
import sys
from collections import defaultdict
from typing import Optional

root_dir = os.getcwd()
sys.path.append(root_dir)
import queue

import numpy as np
import wcwidth
from manim import *
from manim_voiceover import VoiceoverScene

from utils.edgetts_service import EdgeTTSService

################################################################################
# 数据结构与辅助函数
################################################################################

DEPTH_WITH_RECTANGLE = 3


class Node:
    """
    表示思维导图中的节点
    """

    def __init__(self, data: str, children: Optional[list["Node"]] = None):
        self.data: str = data
        self.children: list["Node"] = children if children is not None else []
        self.parent: Optional["Node"] = None
        for child in self.children:
            child.parent = self
        self.depth: int = 0
        self.pos: np.ndarray = np.array([0.0, 0.0, 0.0])
        self.manim_object: Optional[Mobject] = None
        self.line_object: Optional[Mobject] = None
        self.direction: str = "right"
        self.color: Optional[ManimColor] = None

    def add_child(self, child: "Node") -> None:
        """
        为当前节点添加子节点，同时建立双向关联
        """
        child.parent = self
        self.children.append(child)


def assign_levels(node: Node, depth: int = 0) -> None:
    """
    递归设置各节点的深度值
    """
    node.depth = depth
    for child in node.children:
        assign_levels(child, depth + 1)


def get_all_nodes(node: Node) -> list[Node]:
    """
    递归收集树中所有节点
    """
    nodes = [node]
    for child in node.children:
        nodes.extend(get_all_nodes(child))
    return nodes


def assign_direction(node: Node, direction: str) -> None:
    """
    递归为子树中的每个节点设置方向
    """
    node.direction = direction
    for child in node.children:
        assign_direction(child, direction)


def get_target_text_width(node_num: int) -> int:
    """
    根据当前层级的节点数确定文本换行宽度
    """
    thresholds = [(5, 8), (10, 15), (15, 25), (20, 40)]
    for threshold, width in thresholds:
        if node_num < threshold:
            return width
    return 48


def create_wrapped_text(text: str, width: int) -> str:
    """
    根据指定宽度将文本内容换行
    """
    lines = []
    current_line = []
    current_width = 0
    for char in text:
        char_width = wcwidth.wcwidth(char)
        if char_width < 0:
            char_width = 1
        if current_width + char_width > width and current_line:
            lines.append("".join(current_line))
            current_line = []
            current_width = 0
        current_line.append(char)
        current_width += char_width
    if current_line:
        lines.append("".join(current_line))
    return "\n".join(lines)


def layout_subtree_vertical(node: Node, y_offset: float, sibling_distance: float) -> tuple[float, float]:
    """
    对子树进行垂直布局，返回最终 y 轴偏移值和当前节点的 y 坐标
    """
    if not node.children:
        node.pos[1] = y_offset
        return y_offset + sibling_distance, node.pos[1]
    child_ys = []
    current_offset = y_offset
    for child in node.children:
        current_offset, child_y = layout_subtree_vertical(child, current_offset, sibling_distance)
        child_ys.append(child_y)
    node_y = (min(child_ys) + max(child_ys)) / 2
    node.pos[1] = node_y
    return current_offset, node_y


def get_tree_bounding_box(root: Node) -> tuple[float, float, float, float]:
    """
    返回树中所有节点构成的包围盒（min_x, max_x, min_y, max_y）
    """
    all_nodes = get_all_nodes(root)
    lefts = [node.manim_object.get_left()[0] for node in all_nodes if node.manim_object]
    rights = [node.manim_object.get_right()[0] for node in all_nodes if node.manim_object]
    bottoms = [node.manim_object.get_bottom()[1] for node in all_nodes if node.manim_object]
    tops = [node.manim_object.get_top()[1] for node in all_nodes if node.manim_object]
    return min(lefts), max(rights), min(bottoms), max(tops)


def count_leaves(node: Node) -> int:
    """
    递归计算节点子树中的叶子节点数
    """
    if not node.children:
        return 1
    return sum(count_leaves(child) for child in node.children)


def get_left_and_right_subtree_nodes(root: Node, layout: str) -> tuple[list[Node], list[Node]]:
    """
    将根节点的子树依据布局方式划分为左侧和右侧节点集合
    """
    if layout == "left_to_right":
        left_nodes = []
        right_nodes = sum([get_all_nodes(child) for child in root.children], [])
    elif layout == "right_to_left":
        left_nodes = sum([get_all_nodes(child) for child in root.children], [])
        right_nodes = []
    else:  # balance 布局：前半节点放右侧，后一半放左侧
        total_leaves = sum(count_leaves(child) for child in root.children)
        half_leaves = total_leaves / 2.0
        cumulative = 0
        split_idx = 0
        for i, child in enumerate(root.children):
            cumulative += count_leaves(child)
            if cumulative >= half_leaves:
                split_idx = i + 1
                break
        # 按原逻辑，前半部分放右侧，后半部分放左侧
        right_children = root.children[:split_idx]
        left_children = root.children[split_idx:]
        left_nodes = sum([get_all_nodes(child) for child in left_children], [])
        right_nodes = sum([get_all_nodes(child) for child in right_children], [])
    return left_nodes, right_nodes


################################################################################
# 布局类封装
################################################################################


class MindMapLayout:
    def __init__(
        self,
        scene: VoiceoverScene,
        root: Node,
        level_distance: float = 4.0,
        sibling_distance: float = 1.0,
        layout: str = "left_to_right",
    ):
        self.scene = scene
        self.root = root
        self.level_distance = level_distance
        self.sibling_distance = sibling_distance
        assign_levels(self.root)
        self.all_nodes: list[Node] = get_all_nodes(self.root)
        self.text_colors = [PINK, MAROON, PURPLE, BLUE, RED, TEAL, TEAL_E, GREEN, RED_D, BLUE_E, MAROON_B]
        # random.shuffle(self.text_colors)
        self.colors = [
            (rgb_to_color([0.91, 0.94, 0.98]), rgb_to_color([0.2, 0.3, 0.4])),  # 淡蓝色背景,深蓝色文字
            (rgb_to_color([0.95, 0.90, 0.93]), rgb_to_color([0.4, 0.2, 0.3])),  # 淡粉色背景,深红色文字
            (rgb_to_color([0.90, 0.95, 0.91]), rgb_to_color([0.2, 0.4, 0.3])),  # 淡绿色背景,深绿色文字
            (rgb_to_color([0.95, 0.93, 0.88]), rgb_to_color([0.4, 0.3, 0.2])),  # 淡黄色背景,深棕色文字
            (rgb_to_color([0.92, 0.90, 0.95]), rgb_to_color([0.3, 0.2, 0.4])),  # 淡紫色背景,深紫色文字
            (rgb_to_color([0.89, 0.93, 0.95]), rgb_to_color([0.2, 0.3, 0.4])),  # 淡青色背景,深蓝色文字
            (rgb_to_color([0.98, 0.94, 0.91]), rgb_to_color([0.35, 0.25, 0.25])),  # 淡橙色背景,深橙色文字
            (rgb_to_color([0.91, 0.95, 0.96]), rgb_to_color([0.25, 0.3, 0.35])),  # 淡薄荷色背景,深蓝灰色文字
        ]
        self.left_subtree_nodes, self.right_subtree_nodes = get_left_and_right_subtree_nodes(self.root, layout)
        for node in self.left_subtree_nodes:
            node.direction = "left"
        for node in self.right_subtree_nodes:
            node.direction = "right"
        self.visited = {}
        self.color_index = 0

    def create_manim_objects(self, nodes: list[Node], scale: float = 1.0) -> None:
        """
        为每个节点创建 Manim 文本对象并进行换行设置
        """
        level_nodes: dict[int, list[Node]] = defaultdict(list)
        for node in nodes:
            level_nodes[node.depth].append(node)

        for node in nodes:
            current_level_count = len(level_nodes[node.depth])
            target_width = get_target_text_width(current_level_count)
            wrapped_text = create_wrapped_text(node.data, target_width)
            # 创建圆角矩形，适当增加宽高以便留出边距
            if node.parent.depth > 0 and node.parent.color is not None:
                color = node.parent.color
                node.color = color
            else:
                color, text_color = self.colors[self.color_index % len(self.colors)]
                node.color = color
                self.color_index += 1

            if node.depth < DEPTH_WITH_RECTANGLE:
                text_obj = Paragraph(
                    wrapped_text,
                    font_size=24,
                    color=text_color,
                    alignment="center",
                    font="Microsoft YaHei",
                    weight=BOLD if node.depth < DEPTH_WITH_RECTANGLE else NORMAL,
                ).scale(scale)
                rect = RoundedRectangle(
                    corner_radius=0.5 * min(text_obj.width + 0.75, text_obj.height + 0.75),
                    width=text_obj.width + 0.75,
                    height=text_obj.height + 0.75,
                    fill_color=color,
                    fill_opacity=1,
                )
                # 调整文本在矩形中的位置
                group = VGroup(rect, text_obj)
                group.move_to(text_obj.get_center())
                node.manim_object = group
            else:
                text_obj = Paragraph(
                    wrapped_text,
                    font_size=24,
                    color=self.text_colors[node.depth],
                    alignment="center",
                    font="Microsoft YaHei",
                    weight=BOLD if node.depth < DEPTH_WITH_RECTANGLE else NORMAL,
                ).scale(scale)
                ul = Underline(text_obj).set_color(GRAY).set_stroke(width=2)
                group = VGroup(ul, text_obj)
                group.move_to(text_obj.get_center())
                node.manim_object = group

    def horizontal_layout(self) -> None:
        """
        根据节点深度与方向设置水平坐标
        """
        for node in self.all_nodes:
            offset = self.level_distance * node.depth
            node.pos[0] = offset * (-1 if node.direction == "left" else 1)

    def vertical_layout(self) -> None:
        """
        调整左右子树的垂直分布，使新根的 y 坐标处于左右子树中心的均值位置
        """
        root_y = self.root.pos[1]

        def get_children_center(children: list[Node]) -> float:
            if not children:
                return root_y
            dummy = Node("dummy", children=children)
            dummy.pos = np.copy(self.root.pos)
            _, center = layout_subtree_vertical(dummy, root_y, self.sibling_distance)
            return center

        left_children = [child for child in self.root.children if child.direction == "left"]
        right_children = [child for child in self.root.children if child.direction == "right"]

        left_center = get_children_center(left_children)
        right_center = get_children_center(right_children)
        new_root_y = (left_center + right_center) / 2
        self.root.pos[1] = new_root_y

        def shift_subtree(node: Node, delta: float) -> None:
            node.pos[1] += delta
            for child in node.children:
                shift_subtree(child, delta)

        for children, center in (
            (left_children, left_center),
            (right_children, right_center),
        ):
            delta = new_root_y - center
            for child in children:
                shift_subtree(child, delta)

        # 确保所有直接子节点的 parent 指针指向根节点
        for child in self.root.children:
            child.parent = self.root

    def align_mobject_to_node(self, node: Node) -> None:
        """
        使节点对应的 Manim 对象移动到指定位置，并根据父节点方向调整对齐方式
        """
        if node.manim_object:
            node.manim_object.move_to(node.pos)
            if node.parent and node.manim_object:
                alignment = LEFT if node.direction == "right" else RIGHT
                node.manim_object.align_to(node.pos, alignment)

    def align_mobjects(self) -> None:
        """
        对所有节点进行对齐操作
        """
        for node in self.all_nodes:
            self.align_mobject_to_node(node)

    def fix_overlap(self) -> None:
        """
        调整节点重叠：每层内进行缩放并保持父子节点之间左右不重叠
        """
        level_groups: dict[int, dict[str, list[Node]]] = defaultdict(lambda: {"left": [], "right": []})
        for node in self.all_nodes:
            level_groups[node.depth][node.direction].append(node)

        def adjust_node_overlap(node: Node) -> None:
            if node.parent and node.manim_object and node.parent.manim_object:
                parent_obj = node.parent.manim_object
                if node.direction == "right":
                    if node.manim_object.get_left()[0] < parent_obj.get_right()[0] + LARGE_BUFF:
                        node.pos[0] = parent_obj.get_right()[0] + LARGE_BUFF
                        node.manim_object.align_to(node.pos, LEFT)
                else:
                    if node.manim_object.get_right()[0] > parent_obj.get_left()[0] - LARGE_BUFF:
                        node.pos[0] = parent_obj.get_left()[0] - LARGE_BUFF
                        node.manim_object.align_to(node.pos, RIGHT)

        def scale_nodes(nodes: list[Node]) -> None:
            if len(nodes) < 2:
                return
            scale_factor = 1.0
            for lower, upper in zip(nodes, nodes[1:]):
                gap = upper.pos[1] - lower.pos[1]
                candidate = 2 * gap / (lower.manim_object.height + upper.manim_object.height + self.sibling_distance)
                scale_factor = min(scale_factor, candidate)
            if scale_factor < 1.0:
                for node in nodes:
                    node.manim_object.scale(scale_factor)

        max_depth = max(level_groups.keys(), default=0)
        for depth in range(1, max_depth + 1):
            for direction in ("left", "right"):
                nodes = level_groups[depth][direction]
                if nodes:
                    nodes.sort(key=lambda n: n.pos[1])
                    scale_nodes(nodes)
                    for node in nodes:
                        self.align_mobject_to_node(node)
                        adjust_node_overlap(node)

    def play_animation(
        self,
        scene: VoiceoverScene,
        animations,
        run_time: float = 2,
        script: str = None,
        tts_duration: float = 0,
    ) -> None:
        if script:
            with scene.voiceover(script):
                scene.play(animations, run_time=run_time)
        else:
            scene.play(animations, run_time=run_time)
            scene.wait(max(0, tts_duration - run_time) + 0.1)

    def _find_parent_and_child_nodes(self, cur_node: Node) -> Tuple[Optional[Node], Optional[Node]]:
        """查找当前节点对应的父节点和子节点"""
        parent_node, child_node = None, None
        if cur_node.depth == 1:
            parent_node = cur_node
        elif cur_node.depth >= 2:
            cnode = cur_node
            while cnode:
                if cnode.depth == 2:
                    child_node = cnode
                elif cnode.depth == 1:
                    parent_node = cnode
                    break
                cnode = cnode.parent
        return parent_node, child_node

    def _initialize_visited_tracking(self, parent_node: Node) -> None:
        """初始化访问跟踪"""
        if parent_node not in self.visited:
            self.visited[parent_node] = {}
            for child in parent_node.children:
                self.visited[parent_node][child] = 0

    def focus_centrol(
        self,
        pre_text: str = None,
        cur_text: str = None,
        scene: Optional[VoiceoverScene] = None,
        run_time: float = 2,
        script: str = None,
        tts_duration: float = None,
        keywords: list = [],
        timeout: float = 1000,
    ) -> None:
        vgroup = []
        cur_node = next((n for n in self.all_nodes if n.data == cur_text), None)
        if cur_node is None:
            raise ValueError(f"Node with text '{cur_text}' not found.")
        parent_node, child_node = None, None
        if cur_node.depth == 1:
            parent_node = cur_node
        elif cur_node.depth >= 2:
            cnode = cur_node
            while cnode:
                if cnode.depth == 2:
                    child_node = cnode
                elif cnode.depth == 1:
                    parent_node = cnode
                    break

                cnode = cnode.parent
        if parent_node not in self.visited:
            self.visited[parent_node] = {}
            for child in parent_node.children:
                self.visited[parent_node][child] = 0
        if child_node is None:
            if pre_text != "-1":
                self.focus(node=parent_node.parent, script=script, scene=scene, run_time=1, tts_duration=0)
            self.focus(node=parent_node, script=script, scene=scene, run_time=run_time, tts_duration=0)
            vgroup = self.display_keywords(keywords=keywords)
            # self.animate_dot_along_edge(parent_node)
        else:
            self.visited[parent_node][child_node] = 1
            self.focus_child(
                parent_node=parent_node,
                child_list=self.visited[parent_node],
                script=script,
                scene=scene,
                run_time=run_time,
                tts_duration=0,
            )
            # self.animate_dot_to_node(child_node, change_color=True)
            vgroup = self.display_keywords(keywords=keywords)

            # end_time = self.scene.renderer.time
            # if child_node.children and len(child_node.children) > (timeout - end_time + begin_time):
            #     self.animate_dot_along_edge(child_node, change_color=True)
            # else:
            #     self.animate_dot_along_edge_dfs(child_node, change_color=True)
            self.visited[parent_node][child_node] = 2
        return vgroup

    def focus_child(
        self,
        parent_node: Node = None,
        child_list: dict[Node, int] = None,
        scene: Optional[VoiceoverScene] = None,
        display_level: int = 4,
        run_time: float = 2,
        script: str = None,
        tts_duration: float = None,
    ) -> None:
        """
        将指定节点及其子树置于屏幕中心，并通过平移和缩放动画实现焦点切换效果。

        参数:
            node (Node, optional): 指定需要聚焦的节点对象。如果未传入，则根据 text 参数查找相应节点。
            text (str, optional): 指定节点显示文本，通过文本匹配查找对应节点。当 node 参数为 None 时有效。
            scene (Scene, optional): 动画播放所在的场景对象。如果未传入，则使用当前实例中的 scene。
            first_call (bool, optional): 表示是否为第一次调用该函数。第一次调用时动画中直接 FadeIn 展示对象。
            run_time (float, optional): 动画运行时长，单位为秒。

        行为:
            1. 若 user 没有提供节点和文本，则抛出 ValueError。
            2. 根据 provided node 或者 text 查找目标节点。
            3. 根据目标节点及其子树中所有节点的边界计算整体中心和画面平移、缩放因子。
            4. 将所有节点的对应 Manim 对象和连线聚合到一起，构造一个 VGroup。
            5. 如果 first_call 为 True，则先进行缩放和平移，然后通过 FadeIn 动画展示聚合对象；否则通过动画平滑过渡到新焦点。
            6. 最后调用 scene.wait 停顿，确保动画效果完整显示。

        注意:
            确保目标节点及其子树中的所有节点在调用本函数前均已经创建对应 Manim 对象，否则会引发属性访问错误。
        """
        # 获取从根节点到目标节点的路径
        path_nodes = [parent_node]
        # 隐藏所有节点
        for n in self.all_nodes:
            if n.manim_object:
                n.manim_object.set_opacity(0.0)
                if n.line_object:
                    n.line_object.set_stroke(opacity=0.0)

        # 显示路径上的节点及其直接子节点
        shown_nodes = set()
        for path_node in path_nodes:
            # 显示路径节点
            if path_node.manim_object:
                path_node.manim_object.set_opacity(1)
                if path_node.line_object:
                    path_node.line_object.set_stroke(opacity=1)
                shown_nodes.add(path_node)

        # 显示路径节点的所有子节点
        que = queue.Queue()
        if child_list is not None and len(child_list) > 0:
            for child in child_list:
                if child.depth <= display_level:
                    que.put((child, child_list[child]))
        while not que.empty():
            nd, flag = que.get()
            if flag >= 1:
                shown_nodes.add(nd)
                nd.manim_object.set_opacity(1)
            else:
                nd.manim_object.set_opacity(0.1)
            if nd.line_object:
                if flag >= 1:
                    nd.line_object.set_stroke(opacity=1)
                else:
                    nd.line_object.set_stroke(opacity=0.1)
            for child in nd.children:
                if child.depth <= display_level:
                    que.put((child, flag))

        # line.set_color(GREY)

        # 执行原有的聚焦动画
        print(f"focus: {parent_node.data}, script {script}")
        if scene is None:
            scene = self.scene
        subtree_nodes = get_all_nodes(parent_node)
        lefts = [n.manim_object.get_left()[0] for n in subtree_nodes if n.manim_object]
        rights = [n.manim_object.get_right()[0] for n in subtree_nodes if n.manim_object]
        bottoms = [n.manim_object.get_bottom()[1] for n in subtree_nodes if n.manim_object]
        tops = [n.manim_object.get_top()[1] for n in subtree_nodes if n.manim_object]

        min_x, max_x = min(lefts), max(rights)
        min_y, max_y = min(bottoms), max(tops)
        tree_center = np.array([(max_x + min_x) / 2, (max_y + min_y) / 2, 0])
        scene_center = np.array([0, 0, 0])
        translation = scene_center - tree_center

        frame_width = scene.camera.frame_width
        frame_height = scene.camera.frame_height
        tree_width = max_x - min_x
        tree_height = max_y - min_y
        scale_factor = min(0.9 * frame_width / tree_width, 0.9 * frame_height / tree_height)

        all_group = VGroup(*[n.manim_object for n in self.all_nodes])
        all_group.add(*[n.line_object for n in self.all_nodes if n.line_object])

        animation = all_group.animate.scale(scale_factor, about_point=tree_center).shift(translation)
        self.play_animation(
            scene,
            animation,
            run_time=run_time,
            script=script,
            tts_duration=tts_duration,
        )
        scene.wait(0.1)

    def get_level(self, cur_text: str):
        cur_node = next((n for n in self.all_nodes if n.data == cur_text), None)
        if cur_node is None:
            raise ValueError(f"Node with text '{cur_text}' not found.")
        return cur_node.depth

    def focus(
        self,
        node: Node = None,
        text: str = None,
        scene: Optional[VoiceoverScene] = None,
        first_call: bool = False,
        display_level: int = 4,
        run_time: float = 2,
        script: str = None,
        tts_duration: float = None,
    ) -> None:
        """
        将指定节点及其子树置于屏幕中心，并通过平移和缩放动画实现焦点切换效果。

        参数:
            node (Node, optional): 指定需要聚焦的节点对象。如果未传入，则根据 text 参数查找相应节点。
            text (str, optional): 指定节点显示文本，通过文本匹配查找对应节点。当 node 参数为 None 时有效。
            scene (Scene, optional): 动画播放所在的场景对象。如果未传入，则使用当前实例中的 scene。
            first_call (bool, optional): 表示是否为第一次调用该函数。第一次调用时动画中直接 FadeIn 展示对象。
            run_time (float, optional): 动画运行时长，单位为秒。

        行为:
            1. 若 user 没有提供节点和文本，则抛出 ValueError。
            2. 根据 provided node 或者 text 查找目标节点。
            3. 根据目标节点及其子树中所有节点的边界计算整体中心和画面平移、缩放因子。
            4. 将所有节点的对应 Manim 对象和连线聚合到一起，构造一个 VGroup。
            5. 如果 first_call 为 True，则先进行缩放和平移，然后通过 FadeIn 动画展示聚合对象；否则通过动画平滑过渡到新焦点。
            6. 最后调用 scene.wait 停顿，确保动画效果完整显示。

        注意:
            确保目标节点及其子树中的所有节点在调用本函数前均已经创建对应 Manim 对象，否则会引发属性访问错误。
        """
        if node is None and text is None:
            raise ValueError("Either node or text should be provided.")
        if node is None:
            node = next((n for n in self.all_nodes if n.data == text), None)
            if node is None:
                raise ValueError(f"Node with text '{text}' not found.")

        if display_level >= 0:
            # 获取从根节点到目标节点的路径
            path_nodes = []
            current = node
            while current:
                path_nodes.append(current)
                current = current.parent
            path_nodes.reverse()

            # 隐藏所有节点
            for n in self.all_nodes:
                if n.manim_object:
                    n.manim_object.set_opacity(0.0)
                    if n.line_object:
                        n.line_object.set_stroke(opacity=0.0)

            # 显示路径上的节点及其直接子节点
            shown_nodes = set()
            for path_node in path_nodes:
                # 显示路径节点
                if path_node.manim_object:
                    path_node.manim_object.set_opacity(1)
                    if path_node.line_object:
                        path_node.line_object.set_stroke(opacity=1)
                    shown_nodes.add(path_node)

            # 显示路径节点的所有子节点
            que = queue.Queue()
            for child in node.children:
                if child.depth <= display_level:
                    que.put(child)
            while not que.empty():
                nd = que.get()
                shown_nodes.add(nd)
                nd.manim_object.set_opacity(1)
                if nd.line_object:
                    nd.line_object.set_stroke(opacity=1)
                for child in nd.children:
                    if child.depth <= display_level:
                        que.put(child)

        # 执行原有的聚焦动画
        print(f"focus: {node.data}, script {script}")
        if scene is None:
            scene = self.scene
        subtree_nodes = get_all_nodes(node)
        lefts = [n.manim_object.get_left()[0] for n in subtree_nodes if n.manim_object]
        rights = [n.manim_object.get_right()[0] for n in subtree_nodes if n.manim_object]
        bottoms = [n.manim_object.get_bottom()[1] for n in subtree_nodes if n.manim_object]
        tops = [n.manim_object.get_top()[1] for n in subtree_nodes if n.manim_object]

        min_x, max_x = min(lefts), max(rights)
        min_y, max_y = min(bottoms), max(tops)
        tree_center = np.array([(max_x + min_x) / 2, (max_y + min_y) / 2, 0])
        scene_center = np.array([0, 0, 0])
        translation = scene_center - tree_center

        frame_width = scene.camera.frame_width
        frame_height = scene.camera.frame_height
        tree_width = max_x - min_x
        tree_height = max_y - min_y
        scale_factor = min(0.9 * frame_width / tree_width, 0.9 * frame_height / tree_height)

        all_group = VGroup(*[n.manim_object for n in self.all_nodes])
        all_group.add(*[n.line_object for n in self.all_nodes if n.line_object])

        if first_call:
            que = queue.Queue()
            que.put(node)
            all_level_anims = []

            while not que.empty():
                n = que.qsize()
                all_group = VGroup()
                line_group = VGroup()

                # 先收集当前层的所有节点
                current_level_nodes = []
                for i in range(n):
                    nd: Node = que.get()
                    current_level_nodes.append(nd)
                    for child in nd.children:
                        que.put(child)

                # 按父节点分组
                parent_groups = {}
                for nd in current_level_nodes:
                    parent_id = id(nd.parent) if nd.parent else None
                    if parent_id not in parent_groups:
                        parent_groups[parent_id] = []
                    parent_groups[parent_id].append(nd)

                # 按父节点顺序添加到all_group
                for parent_id, nodes in parent_groups.items():
                    for nd in nodes:
                        all_group.add(nd.manim_object)
                        if nd.line_object:
                            line_group.add(nd.line_object)

                all_group.scale(scale_factor, about_point=tree_center)
                all_group.shift(translation)

                level_anims = []
                if len(line_group) != 0:
                    line_group.scale(scale_factor, about_point=tree_center)
                    line_group.shift(translation)
                    line_animations = [Create(line_object) for line_object in line_group]
                    if display_level < 0:
                        level_anims.append(AnimationGroup(*line_animations))
                    else:
                        scene.play(AnimationGroup(*line_animations), run_time=run_time)
                if display_level < 0:
                    node_anim = AnimationGroup(*[FadeIn(mob) for mob in all_group])
                    level_anims.append(node_anim)
                    all_level_anims.append(AnimationGroup(*level_anims))
                else:
                    scene.play(FadeIn(all_group), run_time=run_time)
            if display_level < 0:
                scene.play(AnimationGroup(*all_level_anims, lag_ratio=0.5), run_time=run_time)
        else:
            animation = all_group.animate.scale(scale_factor, about_point=tree_center).shift(translation)
            self.play_animation(
                scene,
                animation,
                run_time=run_time,
                script=script,
                tts_duration=tts_duration,
            )
        scene.wait(0.1)

    def apply_layout(
        self,
        scene: Optional[VoiceoverScene] = None,
        display_level: int = 2,
        script: str = None,
        tts_duration: float = None,
        create_mind: bool = True,
    ) -> None:
        """
        执行整体布局：创建节点对象、水平/垂直排列、调整重叠并生成连线
        """
        if scene is None:
            scene = self.scene
        # 为根节点创建 Manim 对象
        color, text_color = self.colors[self.color_index % len(self.colors)]
        self.color_index += 1
        text_obj = Paragraph(
            create_wrapped_text(self.root.data, 10),
            font_size=40,
            color=text_color,
            alignment="center",
            font="Microsoft YaHei",
            weight=BOLD,
        )
        rect = RoundedRectangle(
            corner_radius=0.5 * min(text_obj.width + 0.75, text_obj.height + 0.75),
            width=text_obj.width + 0.75,
            height=text_obj.height + 0.75,
            fill_color=color,
            fill_opacity=1,
        )
        # 调整文本在矩形中的位置
        group = VGroup(rect, text_obj)
        group.move_to(text_obj.get_center())
        self.root.manim_object = group
        self.root.color = color
        # 分别为左右子树创建 Manim 对象
        self.create_manim_objects(self.left_subtree_nodes)
        self.create_manim_objects(self.right_subtree_nodes)
        self.horizontal_layout()
        self.vertical_layout()
        self.fix_overlap()
        self.align_mobjects()
        if scene is not None:
            line_config = LineConfig(style="curved", width=2, color=GRAY)
            get_edge_lines(self.root, self.text_colors, line_config)
            for node in self.all_nodes:
                if node.depth >= DEPTH_WITH_RECTANGLE:
                    current_center = node.manim_object.get_center()
                    offset = current_center - node.manim_object.get_bottom()
                    node.manim_object.shift(offset)
            if create_mind:
                self.focus(
                    self.root,
                    first_call=True,
                    run_time=2,
                    display_level=display_level,
                    script=script,
                    tts_duration=tts_duration,
                )

    def toggle_visibility(
        self,
        visible: bool,
        run_time: float = 2,
        script: str = None,
        tts_duration: float = 0,
    ) -> None:
        """
        隐藏或展示整个思维导图
        visible 为 True 时，通过 FadeIn 动效显示，
        为 False 时，通过 FadeOut 动效隐藏。
        """
        # 收集所有节点和连线对应的 Manim 对象
        all_objects = [node.manim_object for node in self.all_nodes if node.manim_object] + [
            node.line_object for node in self.all_nodes if node.line_object
        ]
        group = VGroup(*all_objects)
        if visible:
            animation = FadeIn(group)
        else:
            animation = FadeOut(group)
        self.play_animation(
            self.scene,
            animation,
            run_time=run_time,
            script=script,
            tts_duration=tts_duration,
        )

    def show(self, run_time: float = 1, script: str = None, tts_duration: float = 0) -> None:
        self.toggle_visibility(True, run_time=run_time, script=script, tts_duration=tts_duration)

    def hide(self, run_time: float = 1, script: str = None, tts_duration: float = 0) -> None:
        self.toggle_visibility(False, run_time=run_time, script=script, tts_duration=tts_duration)


################################################################################
# JSON 到树结构及边的绘制
################################################################################


def create_node_from_json(json_data: dict, current_level: int, max_level: int) -> Node:
    """
    递归根据 JSON 数据生成节点树
    """
    children = [
        create_node_from_json(child, current_level + 1, max_level)
        for child in json_data.get("子章节", [])
        if current_level <= max_level
    ]
    # 注意：此处进行 children 的反转，确保添加顺序与原逻辑一致
    return Node(json_data["标题"], children=list(reversed(children)))


class LineConfig(dict):
    def __init__(
        self,
        style: str = "straight",
        width: int = 2,
        color=GRAY,
        curve_factor: float = 0.2,
    ):
        super().__init__()
        self["style"] = style
        self["width"] = width
        self["color"] = color
        self["curve_factor"] = curve_factor


def get_edge_lines(node: Node, colors: list, config: Optional[LineConfig] = None) -> list[Mobject]:
    """
    递归生成节点之间的连线
    """
    if config is None:
        config = LineConfig()
    for child in node.children:
        # print(child.depth, colors[child.depth])
        if node.manim_object and child.manim_object:
            if child.direction == "right":
                start = node.manim_object.get_right()
                end = child.manim_object.get_left()
            else:
                start = node.manim_object.get_left()
                end = child.manim_object.get_right()

            if config["style"] == "straight":
                line = Line(start, end, stroke_width=config["width"], color=colors[child.depth])
            elif config["style"] == "curved":
                parent_y = node.manim_object.get_center()[1]
                child_y = child.manim_object.get_center()[1]
                direction_factor = 1 if child_y >= parent_y else -1
                curve_factor = config.get("curve_factor", 0.2)
                mid_x = (start[0] + end[0]) / 2
                ctrl1 = np.array(
                    [
                        mid_x,
                        start[1] + direction_factor * curve_factor * abs(end[0] - start[0]) * 0.5,
                        0,
                    ]
                )
                ctrl2 = np.array(
                    [
                        mid_x,
                        end[1] - direction_factor * curve_factor * abs(end[0] - start[0]) * 0.5,
                        0,
                    ]
                )
                line = CubicBezier(
                    start,
                    ctrl1,
                    ctrl2,
                    end,
                    stroke_width=config["width"],
                    color=colors[child.depth],
                )
                # color=config["color"],
            child.line_object = line
            get_edge_lines(child, colors, config)


def create_mindmap(
    scene: VoiceoverScene,
    data_path: str,
    max_level: int = 3,
    display_level: int = 4,
    script: str = None,
    tts_duration: float = None,
    create_mind: bool = True,
) -> MindMapLayout:
    with open(data_path, encoding="utf-8") as f:
        data = json.load(f)
    root = create_node_from_json(data, current_level=0, max_level=max_level)
    mindmap = MindMapLayout(scene, root, level_distance=2.5, sibling_distance=0.5, layout="balance")
    mindmap.apply_layout(display_level=display_level, script=script, tts_duration=tts_duration, create_mind=create_mind)
    return mindmap


################################################################################
# Manim Scene
################################################################################


class SpacetreeMindMap(VoiceoverScene):
    def construct(self):
        self.camera.background_color = WHITE
        self.set_speech_service(
            EdgeTTSService(global_speed=1.2, voice="zh-CN-YunxiNeural"),
            create_subcaption=True,
        )
        # 生成节点树
        layout: MindMapLayout = create_mindmap(
            self,
            "output/outline_ai.json",
            max_level=2,
            display_level=5,
            script="你是否已经感受到,一场前所未有的技术革命正在深刻地改变着我们的世界?人工智能,特别是大模型,正以惊人的速度重塑我们的认知方式,驱动着各行各业的变革,并引领着我们走向一个充满无限可能的未来。那么,究竟什么是大模型?它又将如何影响我们的生活? 让我们一起开启这场大模型之旅。",
        )
        root = layout.root
        # layout.animate_dot_along_edge_dfs(root, change_color=True)

        # 使用 Transform 动画实现不同节点作为中心的效果
        if root.children:
            if len(root.children) > 1:
                layout.focus(
                    root.children[1],
                    script="了解了大模型的基本原理和应用场景,如何才能更好地驾驭它?本章节将为你揭秘如何利用大模型提升工作效率,以及如何开发定制化的AI应用",
                )
            # 测试圆点沿着连线移动的动画
            # layout.animate_dot_to_node(root.children[1], change_color=True)
            # layout.animate_dot_along_edge_dfs(root.children[1])
            layout.focus(
                root.children[0],
                script="大模型仍在快速发展,它的未来将走向何方? 让我们一起展望,共同探讨大模型的未来发展趋势。",
            )
            layout.focus(root, display_level=2, tts_duration=3)
            if root.children[-1].children:
                layout.focus(
                    root.children[-1].children[-1],
                    script="大模型的崛起并非一蹴而就,而是经历了漫长的历史演进。在这一章节,我们将回顾大模型从RNN到Transformer的认知革命,感受深度学习的“ 巨人肩膀” 。 同时,我们将聚焦GPT系列,看看智力爆炸的“核反应堆”是如何诞生的。 历史的巨轮滚滚向前,其中蕴藏着哪些关键的突破?",
                )
            layout.focus(root, display_level=2, tts_duration=3)
