#!/usr/bin/env python3
"""
表格生成工具 - 内容结构化组织类
基于内容自动提取结构化数据并生成表格，将复杂信息以清晰的表格形式展示
"""

import json
import os
import re
from typing import Any, Optional, Dict
from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class TableGenerationTool(EnhancementTool):
    """表格生成工具 - 内容结构化组织类"""
    
    tool_name = "table_generation"
    tool_description = "从包含具体数据、参数、规格的内容中提取量化信息并生成精简表格，专门处理可对比的数值、技术参数、特性标识等结构化数据"
    tool_category = ToolCategory.CONTENT_ORGANIZATION
    
    suitable_content_types = ["产品规格对比文档", "技术参数列表", "价格配置表", "性能测试数据", "功能特性清单", "设备配置文档", "评分统计报告"]
    suitable_purposes = ["产品参数对比", "技术规格展示", "价格性能分析", "配置选择指南", "评测数据汇总", "功能差异对比"]
    required_conditions = ["包含具体数值数据", "有明确参数规格", "存在可对比项目", "内容长度适中(>600字符)", "LLM模型可用"]
    
    # 不适用场景描述
    unsuitable_content_types = ["纯文字叙述", "故事情节内容", "操作流程说明", "历史时间线", "理论概念阐述", "情感表达文章", "新闻报道"]
    unsuitable_purposes = ["故事讲述", "流程演示", "历史回顾", "理论教学", "情感沟通", "新闻传播", "创意表达"]
    blocking_conditions = ["内容过短(<600字符)", "纯描述性文字", "无量化数据", "无对比要素", "无LLM模型", "明确拒绝表格展示"]

    def __init__(self, config=None):
        self.config = config
        self.table_agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType
            
            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )
            
            # 定制化系统提示词
            system_prompt = """你是专业的数据结构化专家，专门从内容中提取结构化信息并生成表格。
你的职责是：
1. 识别内容中可以结构化展示的数据和信息
2. 设计合适的表格结构，包括表头和数据行
3. 确保表格数据的准确性和完整性
4. 为表格提供清晰的标题和说明

**表格设计原则**：
1. 表格只包含**数据、参数、特性**，不要描述性文字
2. 设计1-2个数据对比表格，每个表格3-6行
3. **每个单元格严格限制在6个字符以内**
4. 表头用简洁标识符（如"型号"、"价格"、"容量"）
5. 表格内容只能是：数字+单位、特性标识、状态符号、简短代号

**数据提取重点**：
- **数值数据**：价格、尺寸、重量、容量、速度、时间等
- **技术参数**：规格、配置、性能指标、版本号等  
- **特性标识**：支持/不支持用✓/✗，有/无用○/●
- **分类标签**：等级(高/中/低)、类型(A/B/C)、状态(开/关)
- **简短代号**：产品型号、版本代码、简称等

**严禁包含**：
- 完整句子和段落
- 形容词和副词(如"非常好"、"相当不错")  
- 解释性文字(如"这款产品具有...")
- 主观评价(如"值得推荐"、"性价比高")

**单元格内容示例**：
✅ 正确：7999元、128GB、✓、A17芯片、23小时、USB-C
❌ 错误：价格实惠、存储充足、功能强大、值得购买

请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""
            
            self.table_agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "结构化表格数据",
            "typical_output": "1-2个主题表格，每个表格3-6行数据",
            "use_case": "适用于原始内容包含多项可对比数据的场景：如产品价格、技术参数、规格配置、性能指标、评分数据等量化信息。原始数据必须具备明确的对比维度和具体数值，能够提取出结构化的参数表格。"
        }

    def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关、模型可用
        basic_check = (
            self.config and 
            len(content) >= 600 and
            self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )
        
        # 检查agent是否初始化成功
        if self.table_agent is None:
            return False
            
        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)
            
            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "tables_count": len(existing_data.get("tables", [])),
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_with_camel(content, context)
            
            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "tables_count": len(result_data.get("tables", [])),
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_with_camel(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用Camel进行数据提取的处理逻辑"""
        purpose = context.get("purpose", "数据结构化展示")
        
        # 构建结构化提示词
        prompt = f"""**首先评估内容是否适合制作表格**：

**内容材料**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**评估标准**：
请先判断内容是否包含足够的结构化数据：
- ✅ 适合：包含可对比的数值、参数、规格、特性等量化信息
- ❌ 不适合：纯叙述文字、故事内容、理论阐述、缺乏具体数据

**重要**：如果内容不适合制作表格，请直接返回：`{"suitable": false, "reason": "内容缺乏结构化数据"}`

---

**如果内容适合，则基于以下要求提取关键数据制作精简表格**：

**处理目标**：{purpose}

**表格设计原则**：
1. 表格只包含**数据、参数、特性**，不要描述性文字
2. 设计1-2个数据对比表格，每个表格3-6行
3. **每个单元格严格限制在6个字符以内**
4. 表头用简洁标识符（如"型号"、"价格"、"容量"）
5. 表格内容只能是：数字+单位、特性标识、状态符号、简短代号

**数据提取重点**：
- **数值数据**：价格、尺寸、重量、容量、速度、时间等
- **技术参数**：规格、配置、性能指标、版本号等  
- **特性标识**：支持/不支持用✓/✗，有/无用○/●
- **分类标签**：等级(高/中/低)、类型(A/B/C)、状态(开/关)
- **简短代号**：产品型号、版本代码、简称等

**严禁包含**：
- 完整句子和段落
- 形容词和副词(如"非常好"、"相当不错")  
- 解释性文字(如"这款产品具有...")
- 主观评价(如"值得推荐"、"性价比高")

**单元格内容示例**：
✅ 正确：7999元、128GB、✓、A17芯片、23小时、USB-C
❌ 错误：价格实惠、存储充足、功能强大、值得购买

**输出要求**：
```json
{{
    "suitable": true,
    "summary": {{
        "content_theme": "数据主题",
        "target_audience": "{purpose}",
        "table_focus": "对比要点"
    }},
    "tables": [
        {{
            "title": "数据表名",
            "description": "简要说明",
            "headers": ["字段1", "字段2", "字段3"],
            "rows": [
                ["项目A", "数据1", "特性1"],
                ["项目B", "数据2", "特性2"],
                ["项目C", "数据3", "特性3"]
            ],
            "table_type": "对比表",
            "key_insights": ["关键发现1", "关键发现2"]
        }}
    ],
    "metadata": {{
        "extraction_quality": "精准数据",
        "table_count": "1-2",
        "data_completeness": "核心参数完整"
    }}
}}
```

**关键要求**：
- 先评估是否适合制作表格，不适合直接返回 suitable: false
- 表格必须是数据导向，不是文字导向
- 每个单元格≤6字符，优先数字和符号
- 可直接对比的量化信息
- 避免任何主观描述和冗余文字"""

        try:
            # 调用Camel agent进行处理
            response = self.table_agent.step(prompt)
            response_content = response.msgs[0].content
            
            # 提取JSON
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个响应
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            
            # 检查内容是否适合制作表格
            if not result_data.get("suitable", True):
                logger.info(f"内容不适合制作表格: {result_data.get('reason', '内容缺乏结构化数据')}")
                return None
            
            return result_data
            
        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: Dict[str, Any]) -> str:
        """生成工具输出的介绍文本 - 返回完整JSON结果"""
        data = tool_result.get("data", {})
        
        if not data:
            return ""

        # 返回完整的JSON数据，格式化为可读的字符串
        intro = f"## 📊 {self.tool_description}\n\n"
        intro += "### 完整表格数据\n\n"
        intro += "```json\n"
        intro += json.dumps(data, ensure_ascii=False, indent=2)
        intro += "\n```\n\n"
        
        return intro 